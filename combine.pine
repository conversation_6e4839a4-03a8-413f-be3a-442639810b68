// This Pine Script™ code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © ChartPrime

//@version=6
indicator('RSI-Adaptive T3 [ChartPrime]', overlay = true)


// --------------------------------------------------------------------------------------------------------------------}
// 📌 𝙐𝙎𝙀𝙍 𝙄𝙉𝙋𝙐𝙏𝙎
// --------------------------------------------------------------------------------------------------------------------{

src         = close
rsiLen      = input.int(14, 'RSI Length', group = "T3")
minLen      = input.int(5, 'Min T3 Length', group = "T3")
maxLen      = input.int(50, 'Max T3 Length', group = "T3")
v           = input.float(0.7, 'T3 Volume Factor', step = 0.01, maxval = 2, minval = 0.1, group = "T3")
color_up    = input.color(#21b8f3)
color_dn    = input.color(#fd761b)


grp_vol_b   = 'Volatility Bands'
show_bands  = input.bool(true, 'Display', group = grp_vol_b, inline = "vol")
vol_col     = input.color(#21c9f380, "", group = grp_vol_b, inline = "vol")
volat       = input.int(100, 'Volatility', group = grp_vol_b)


// --------------------------------------------------------------------------------------------------------------------}
// 📌 𝙄𝙉𝘿𝙄𝘾𝘼𝙏𝙊𝙍 𝘾𝘼𝙇𝘾𝙐𝙇𝘼𝙏𝙄𝙊𝙉𝙎
// --------------------------------------------------------------------------------------------------------------------{

// Step 1: Adaptive length via RSI
rsi = ta.rsi(src, rsiLen)
rsi_scale = 1 - rsi / 100
len = math.round(minLen + (maxLen - minLen) * rsi_scale)

pine_ema(src, length) =>
    alpha = 2 / (length + 1)
    sum = 0.0
    sum := na(sum[1]) ? src : alpha * src + (1 - alpha) * nz(sum[1])
    sum

// Step 2: T3 with adaptive length
e1 = pine_ema(src, len)
e2 = pine_ema(e1, len)
e3 = pine_ema(e2, len)
e4 = pine_ema(e3, len)
e5 = pine_ema(e4, len)
e6 = pine_ema(e5, len)

c1 = -v * v * v
c2 = 3 * v * v + 3 * v * v * v
c3 = -6 * v * v - 3 * v - 3 * v * v * v
c4 = 1 + 3 * v + v * v * v + 3 * v * v
t3 = c1 * e6 + c2 * e5 + c3 * e4 + c4 * e3

t3_col = t3 > t3[2] ? color_up : color_dn

stdv = ta.stdev(t3, volat)

// --------------------------------------------------------------------------------------------------------------------}
// 📌 𝙑𝙄𝙎𝙐𝘼𝙇𝙄𝙕𝘼𝙏𝙄𝙊𝙉
// --------------------------------------------------------------------------------------------------------------------{

pt31 = plot(t3, color = t3_col, linewidth = 1, editable = false)
pt32 = plot(t3[2], color = t3_col, linewidth = 1, editable = false)
fill(pt31, pt32, color.new(t3_col, 90))

pu = plot(t3 + stdv, "Upper Volatility Band", style = plot.style_cross, color = vol_col, display = show_bands ? display.all : display.none)
pl = plot(t3 - stdv, "Lower Volatility Band", style = plot.style_cross, color = vol_col, display = show_bands ? display.all : display.none)
fill(pu, pl, color.new(vol_col, 95), display = show_bands ? display.all : display.none)


plotchar(ta.crossover(t3, t3[2]) or ta.crossunder(t3, t3[2]) ? t3 : na, "", "🞛", location.absolute, t3_col)

if barstate.islast
    tbl = table.new(position.middle_right, 10, 10)

    tbl.cell(0, 1, '', text_color = chart.fg_color, text_halign = text.align_right)
    tbl.cell(1, 1, '▼' + str.tostring(maxLen), text_color = color_up)

    tbl.cell(0, 3, '', text_color = chart.fg_color, text_halign = text.align_right)
    tbl.cell(1, 3, '▲' + str.tostring(minLen), text_color = color_dn)

    tbl.cell(0, 2, 'RSI Adaptive Length:', text_color = chart.fg_color)
    tbl.cell(1, 2, str.tostring(len), text_color = color.from_gradient(len, minLen, maxLen, color_dn, color_up))

// --------------------------------------------------------------------------------------------------------------------}

